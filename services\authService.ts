
import { EP } from "@/configs/constants/api";
import { api<PERSON>all, handleApiError, createApiClient } from "@/lib/apiUtils";
import { DataResponseType, GoogleProfile } from "@/types";
import { getApiEndpoint } from "@/helpers/handleApiUrl";

/**
 * Login API call without token requirement
 */
const loginApiCall = async <T>(
    endpoint: string[],
    data: any
): Promise<DataResponseType<T>> => {
    try {
        const api = createApiClient(); // No token for login
        const url = getApiEndpoint(endpoint);
        const response = await api.post<DataResponseType<T>>(url, data);

        // Check if response is valid
        if (!response.data) {
            return {
                error: true,
                status: response.status || 500,
                message: 'Empty response from server',
                data: null as any
            };
        }

        // If response.data is not in expected format, wrap it
        if (typeof response.data === 'object' && !('success' in response.data) && !('error' in response.data)) {
            return {
                error: false,
                status: response.status || 200,
                message: 'Login successful',
                data: response.data as T
            };
        }

        return response.data;
    } catch (error: any) {
        console.error('❌ Login API call failed:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message,
            url: error.config?.url
        });

        // Return error in expected format
        return {
            error: true,
            status: error.response?.status || 500,
            message: error.response?.data?.message || error.response?.data?.msg || error.message || 'Login failed',
            data: null as any
        };
    }
};

/**
 * Login with email and password
 * @param email User email
 * @param password User password
 * @returns Login response with token
 */
export const loginWithEmailPassword = async (email: string, password: string): Promise<DataResponseType<string>> => {
    try {
        return await loginApiCall<string>(
            [EP.API, EP.V1, EP.USER, EP.LOGIN],
            { email, password }
        );
    } catch (error) {
        return handleApiError(error, 'Email Login');
    }
};

/**
 * Login with Google profile
 * @param profile Google profile data
 * @returns Login response with token
 */
export const loginWithGoogleProfile = async (profile: GoogleProfile): Promise<DataResponseType<string>> => {
    try {
        console.log('🔐 Sending Google profile to backend:', profile);

        const result = await loginApiCall<string>(
            [EP.API, EP.V1, EP.USER, EP.LOGIN, EP.GOOGLE],
            profile
        );

        console.log('✅ Google login API response:', result);
        console.log('🎯 LOGIN SUCCESS DATA STRUCTURE:');
        console.log('  - Error:', result?.error);
        console.log('  - Data (Token):', result?.data);
        console.log('  - Message:', result?.msg);
        console.log('  - Full Response:', JSON.stringify(result, null, 2));

        return result;
    } catch (error) {
        console.error('❌ Google login API error:', error);
        return handleApiError(error, 'Google Login');
    }
};

