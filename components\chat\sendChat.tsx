import React from 'react'
import { ChatInput } from '@/components/ui/ChatInput'
import { useSendChatForm } from '@/hooks/useSendChatForm'
import type { SendChatComponentProps } from '@/types/sendChat'

/**
 * SendChat component - handles chat message input and submission
 * Now uses the reusable ChatInput component
 */
export function SendChat({ conversationId, promptId, onConversationUpdate, messages, setMessages }: SendChatComponentProps) {
    const {
        inputValue,
        setInputValue,
        isLoading,
        handleSubmit,
        handleKeyPress
    } = useSendChatForm({ conversationId, promptId, onConversationUpdate, messages, setMessages })

    return (
        <ChatInput
            value={inputValue}
            onChange={setInputValue}
            onSubmit={handleSubmit}
            onKeyDown={handleKeyPress}
            isLoading={isLoading}
            placeholder="Hỏi bất kỳ điều gì bạn muốn..."
            showAttachments={true}
            showVoice={true}
        />
    )
}
