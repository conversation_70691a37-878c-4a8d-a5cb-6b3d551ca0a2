import { decryptAesCBC } from '@/lib/crypto';
import { EP } from "@/configs/constants/api";
import { getCookie } from 'cookies-next/client';
import { COOKIE_KEYS } from '@/types/auth';
import {
    apiCall,
    fetchWithStream,
    handleApiError,
    BASE_API_URL,
    getPromptIdWithFallback,
    getStoredToken,
    getStoredShopId,
    createApiClient
} from "@/lib/apiUtils";

// Re-export for convenience
export { getPromptIdWithFallback } from "@/lib/apiUtils";
import {
    DataResponseType,
    DomainVerificationResponse,
    DomainConfig,
    Message,
    Conversation,
    ChatRequest,
    CreateConversationRequest,
    UserProfile
} from "@/types";

// Re-export types for convenience
export type {
    Message,
    Conversation,
    ChatRequest,
    CreateConversationRequest,
    UserProfile,
    DomainConfig
} from "@/types";


export async function verifyDomain(domain: string): Promise<DomainConfig | null> {
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 8000); // ⏰ Timeout sau 8 giây

    try {
        const res = await fetch(
            `https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=${domain}`,
            { signal: controller.signal }
        );

        if (!res.ok) {
            throw new Error(`API Error: ${res.status} ${res.statusText}`);
        }

        const response: DomainVerificationResponse = await res.json();

        if (response.error || !response.data) {
            return {
                domain,
                allowed: false
            };
        }

        const decryptedData = decryptAesCBC(response.data);
        if (!decryptedData) {
            return {
                domain,
                allowed: false
            };
        }

        try {
            const config = JSON.parse(decryptedData);

            // 👉 Tìm prompt_id với độ ưu tiên từ cao đến thấp
            let prompt_id: string = '';

            if (typeof config.prompt_id === 'string') prompt_id = config.prompt_id;
            else if (typeof config.promptId === 'string') prompt_id = config.promptId;
            else if (typeof config.id === 'string') prompt_id = config.id;
            else if (typeof config._id === 'string') prompt_id = config._id;
            else if (typeof config.prompt === 'string') prompt_id = config.prompt;
            else if (typeof config.prompt === 'object') {
                if (typeof config.prompt._id === 'string') prompt_id = config.prompt._id;
                else if (typeof config.prompt.id === 'string') prompt_id = config.prompt.id;
            } else if (typeof config.prompt_id === 'object') {
                if (typeof config.prompt_id._id === 'string') prompt_id = config.prompt_id._id;
                else if (typeof config.prompt_id.id === 'string') prompt_id = config.prompt_id.id;
            }

            if (!prompt_id || typeof prompt_id !== 'string') {
                prompt_id = '';
            }

            return {
                domain,
                allowed: true,
                config,
                prompt_id
            };
        } catch (parseError) {
            return {
                domain,
                allowed: false
            };
        }

    } catch (error) {
        if ((error as any).name === 'AbortError') {
            console.error('⏰ Domain verification request timed out.');
        } else {
            console.error('❌ Domain verification error:', error);
        }
        return {
            domain,
            allowed: false
        };
    } finally {
        clearTimeout(timeout);
    }
}

/**
 * Get user profile (GET profile)
 * Uses token from cookie as per requirement
 * @param shop_id Shop ID
 * @returns User profile data
 */
export const getUserProfile = async (shop_id?: string): Promise<DataResponseType<UserProfile>> => {
    try {
        console.log('🚀 Getting user profile...');

        const token = getCookie(COOKIE_KEYS.TOKEN) as string;
        if (!token) {
            throw new Error('No authentication token found');
        }

        // Prepare query params
        const response = await apiCall<UserProfile>(
            'GET',
            [EP.API, EP.V1, EP.USER, EP.PROFILE],
            undefined,

        );
        return response;
    } catch (error) {
        return handleApiError(error, 'Get User Profile');
    }
};


/**
 * Update conversation name
 * POST /api/v1/conversation/update
 * @param conversationId Conversation ID
 * @param name New conversation name
 * @param token Authentication token
 * @returns Update response
 */
export const updateConversationName = async (
    conversationId: string,
    name: string,
    token?: string
): Promise<DataResponseType<any>> => {
    if (!conversationId || typeof conversationId !== 'string') {
        throw new Error(`Invalid conversationId provided: ${JSON.stringify(conversationId)}`);
    }

    if (!name || typeof name !== 'string') {
        throw new Error(`Invalid name provided: ${JSON.stringify(name)}`);
    }

    const finalToken = token || getStoredToken();
    const finalShopId = getStoredShopId();

    if (!finalToken) {
        throw new Error('No authentication token available');
    }

    try {
        console.log('🚀 Calling updateConversationName API...');
        const response = await apiCall<any>(
            'POST',
            [EP.API, EP.V1, EP.CONVERSATION, EP.UPDATE],
            {
                id: conversationId,
                name: name
            },
            undefined,
            finalToken,
            finalShopId || undefined
        );
        return response;
    } catch (error) {
        return handleApiError(error, 'Update Conversation Name');
    }
};

/**
 * Create new conversation
 * POST /api/v1/message/conversation?conversation_id=0
 * @param token Authentication token
 * @returns New conversation response
 */
export const createNewConversationMessage = async (
    token?: string
): Promise<DataResponseType<any>> => {
    const finalToken = token || getStoredToken();
    const finalShopId = getStoredShopId();

    if (!finalToken) {
        throw new Error('No authentication token available');
    }

    try {
        const response = await apiCall<any>(
            'POST',
            [EP.API, EP.V1, EP.MESSAGE, EP.CONVERSATION],
            undefined, // No body data needed
            {
                conversation_id: '0' // Default ID for new conversation
            },
            finalToken,
            finalShopId || undefined
        );

        if (response.data && typeof response.data === 'object') {
            console.log('📋 New conversation details:', {
                id: (response.data as any)._id || (response.data as any).id,
                name: (response.data as any).name,
                created_at: (response.data as any).created_at
            });
        }

        return response;
    } catch (error) {
        console.error('❌ createNewConversationMessage error:', error);
        return handleApiError(error, 'Create New Conversation');
    }
};

/**
 * Get prompt list (conversations) - GET conversations
 * Uses token from cookie as per requirement
 * @param prompt_id Prompt ID
 * @param page Page number
 * @param limit Items per page
 * @param _shop_id Shop ID (optional)
 * @returns List of conversations
 */
export const getPromptList = async<T>(
    prompt_id: string,
    page: number = 1,
    limit: number = 10,
    _shop_id?: string
): Promise<DataResponseType<T>> => {
    // Get token from cookie
    const token = getCookie(COOKIE_KEYS.TOKEN) as string;
    if (!token) {
        throw new Error('No authentication token found');
    }

    // Get prompt_id from store if not provided
    let finalPromptId = prompt_id;
    if (!finalPromptId) {
        try {
            const { useAppStore } = require('@/stores/appStore');
            finalPromptId = useAppStore.getState().getPromptId();
        } catch (e) { }
    }

    if (!finalPromptId || typeof finalPromptId !== 'string') {
        throw new Error(`Invalid prompt_id provided: ${JSON.stringify(finalPromptId)}`);
    }

    try {
        const result = await apiCall<T>(
            'GET',
            [EP.API, EP.V1, EP.CONVERSATION, EP.PROMPT, EP.LIST],
            undefined,
            {
                prompt_id: finalPromptId,
                page: page.toString(),
                limit: limit.toString()
            },
            token,
            undefined
        );
        return result;
    } catch (error: any) {
        return handleApiError(error, 'Get Prompt List');
    }
};

/**
 * Get conversation messages
 * @param conversation_id Conversation ID
 * @param token Authentication token
 * @returns Array of messages
 */
export const getConversationMessages = async (
    conversation_id: string,
    token?: string,
): Promise<Message[]> => {
    // Always get token from Zustand/cookie if not provided
    let finalToken = token;
    if (!finalToken) {
        try {
            const { useAuthStore } = require('@/stores/authStore');
            finalToken = useAuthStore.getState().getToken();
        } catch (e) { }
    }
    if (!finalToken) {
        throw new Error('Token is required for API access');
    }

    // Handle demo, new conversation, or invalid conversation IDs
    if (!conversation_id || conversation_id === '0' || conversation_id === 'demo_1' || conversation_id.startsWith('demo_')) {
        if (conversation_id === '0') {
            console.log('🆕 New conversation (ID=0), returning empty messages array');
        } else {
            console.warn('Demo or invalid conversation ID detected:', conversation_id);
        }
        return []; // Return empty array for new/demo conversations
    }

    try {
        console.log('🚀 Making API call to get messages...');

        // Use the new API endpoint directly
        const api = createApiClient(finalToken);
        const url = `/api/v1/message/conversation?conversation_id=${conversation_id}`;

        console.log('🔗 API URL:', `${BASE_API_URL}${url}`);

        const response = await api.get<DataResponseType<Message[]>>(url, {
            headers: {
                'token': finalToken
            }
        });

        console.log('📊 getConversationMessages API response:', response.data,);

        // Check if response indicates success
        if (response.data.error === false && response.data.status === 200) {
            return response.data.data || [];
        } else {
            console.warn('API returned error:', response.data.msg);
            return [];
        }
    } catch (error: any) {
        console.error('Error fetching conversation messages:', error.response?.data || error.message);

        // If it's a 500 error, return empty array instead of throwing
        if (error.response?.status === 500) {
            console.warn('Server error 500, returning empty messages array');
            return [];
        }

        throw new Error(`Failed to fetch messages: ${error.response?.data?.msg || error.message}`);
    }
};

/**
 * Send chat message (streaming)
 * @param chatRequest Chat request data
 * @param token Authentication token
 * @param shop_id Shop ID
 * @param user_id User ID
 * @param website_id Website ID
 * @returns ReadableStream for streaming response
 */
export const sendChatMessage = async (
    chatRequest: ChatRequest,
    token?: string,
    shop_id?: string,
    user_id?: string,
    website_id?: string,
    abortController?: AbortController
): Promise<ReadableStream<Uint8Array> | null> => {
    // Always get token, shop_id, prompt_id from Zustand/cookie if not provided
    let finalToken = token;
    let finalShopId = shop_id;
    let finalUserId = undefined;
    try {
        // Ưu tiên lấy user_id từ cookie
        finalUserId = getCookie('fchatai_user_id') as string;
    } catch (e) {
        finalUserId = undefined;
    }
    // Nếu không có trong cookie thì lấy từ state
    if (!finalUserId) {
        try {
            const { useAuthStore } = require('@/stores/authStore');
            finalUserId = useAuthStore.getState().user_id;
        } catch (e) {
            finalUserId = undefined;
        }
    }
    // Nếu vẫn không có thì lấy từ chatRequest
    if (!finalUserId) {
        finalUserId = chatRequest.user_id;
    }
    // Token và shop_id vẫn lấy như cũ
    if (!finalToken || !finalShopId) {
        try {
            const { useAuthStore } = require('@/stores/authStore');
            finalToken = finalToken || useAuthStore.getState().getToken();
            finalShopId = finalShopId || useAuthStore.getState().shop_id;
        } catch (e) { }
    }
    if (!finalToken) {
        throw new Error('Token is required for sending messages');
    }

    // Use local API route as proxy to avoid CORS issues
    const url = '/api/chat';

    // Include token and shop_id in the request body for the proxy
    // Lấy website_id từ global state nếu chưa có
    const proxyRequest = {
        ...chatRequest,
        token: finalToken,
        shop_id: finalShopId,
        user_id: finalUserId,
    };

    try {
        console.log('🔗 Using proxy URL:', url);
        console.log('� Base URL:', BASE_API_URL);
        console.log('� Proxy request payload:', proxyRequest);
        return await fetchWithStream(
            url,
            proxyRequest,
            finalToken,
            finalShopId || undefined,
            abortController?.signal
        );
    } catch (error) {
        console.error('❌ Error sending chat message:', error);
        return null;
    }
};


/**
 * Create new conversation
 * @param request Conversation request data
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns Created conversation or null
 */
export const createNewConversation = async (
    request: CreateConversationRequest,
    token?: string,
    shop_id?: string
): Promise<Conversation | null> => {
    try {
        const response = await apiCall<Conversation>(
            'POST',
            [EP.API, EP.V1, EP.CONVERSATION, EP.CREATE],
            request,
            undefined,
            token,
            shop_id
        );

        return response.data || null;
    } catch (error: any) {
        console.error('❌ Error creating new conversation:', error.response?.data || error.message);
        return null;
    }
};

/**
 * Fetch prompt conversations
 * @param prompt_id Prompt ID
 * @param page Page number
 * @param limit Items per page
 * @param token Authentication token
 * @param shop_id Shop ID
 * @returns List of conversations
 */
export async function fetchPromptconversations(prompt_id?: string, page: number = 1, limit: number = 10, token?: string, _shop_id?: string) {
    try {
        // Use utility function to get prompt_id with proper fallback
        const finalPromptId = getPromptIdWithFallback(prompt_id);

        const res = await getPromptList(finalPromptId, page, limit, token);

        // Check if API call was successful
        if (res && res.error === false && res.status === 200) {
            // Check if we have conversations data
            // API can return either:
            // 1. { data: [] } - direct array
            // 2. { data: { conversations: [] } } - nested object
            let conversations: any[] = [];

            if (res.data) {
                if (Array.isArray(res.data)) {
                    // Case 1: Direct array
                    conversations = res.data;
                    console.log('📋 Direct array format, conversations:', conversations.length);
                } else if (res.data && typeof res.data === 'object' && Array.isArray((res.data as any).conversations)) {
                    // Case 2: Nested object
                    conversations = (res.data as any).conversations;
                    console.log('📋 Nested object format, conversations:', conversations.length);
                }
            }

            if (conversations.length > 0) {
                const mappedConversations = conversations.map((item: Conversation) => ({
                    id: item._id,
                    icon: '/favicon.ico',
                    title: item.name || 'Chủ đề AI',
                    messages: [],
                    conversation: item
                }));

                console.log('✅ Successfully parsed conversations:', mappedConversations.length);
                return mappedConversations;
            } else {
                // API successful but no conversations data - return empty array
                console.log('ℹ️ API successful but no conversations found, returning empty array');
                return [];
            }
        } else {
            throw new Error(res?.msg || 'API call failed');
        }
    } catch (err: any) {
        console.error('Lỗi lấy danh sách chủ đề:', err);
        throw new Error(err.message || 'Không thể tải danh sách cuộc trò chuyện');
    }
}
