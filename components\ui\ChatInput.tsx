import React from 'react';
import { Mic, Camera, Send } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  className?: string;
  showAttachments?: boolean;
  showVoice?: boolean;
}

/**
 * Reusable chat input component
 */
export const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSubmit,
  onKeyDown,
  placeholder = "Hỏi bất kỳ điều gì bạn muốn...",
  disabled = false,
  isLoading = false,
  className,
  showAttachments = true,
  showVoice = true
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (onKeyDown) {
      onKeyDown(e);
    }
  };

  return (
    <div className={cn("relative mx-auto w-full max-w-[765px] items-center justify-center", className)}>
      <div className="flex w-full flex-col rounded-t-2xl border border-gray-200 bg-white transition-all duration-300 hover:shadow-xl md:rounded-3xl md:shadow-lg dark:border-gray-600 dark:bg-gray-800">
        <div className="w-full p-4 pb-0">
          <form onSubmit={onSubmit} className="relative">
            <div className="flex w-full flex-col ">

              {/* Main input */}
              <input
                type="text"
                value={value}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={disabled || isLoading}
                className="flex-1 bg-transparent placeholder-slate-400 text-slate-900 dark:text-slate-100 px-10 pt-4 rounded-2xl focus:outline-none focus:ring-0 border-none"
              />

              {/* Action buttons */}
              <div className="flex w-full items-center justify-between gap-2 px-4 pt-3 pb-2">

                {/* Left side - Attachments */}
                <div className="flex items-center gap-2">
                  {showAttachments && (
                    <ActionButton
                      icon={<Camera className="w-5 h-5" />}
                      label="Đính kèm ảnh"
                      disabled={disabled || isLoading}
                    />
                  )}
                </div>

                {/* Right side - Send button */}
                {showVoice && (
                  <ActionButton
                    icon={<Mic className="w-5 h-5" />}
                    label="Ghi âm"
                    disabled={disabled || isLoading}
                  />
                )}
              </div>
            </div>
          </form>
        </div >
      </div>
    </div >
  );
};

/**
 * Action button component for chat input
 */
interface ActionButtonProps {
  icon: React.ReactNode;
  label: string;
  onClick?: () => void;
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  icon,
  label,
  onClick,
  disabled = false
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      aria-label={label}
      className={cn(
        "flex min-h-[40px] w-[40px] items-center justify-center rounded-full border transition-colors",
        disabled
          ? "border-gray-300 text-gray-400 cursor-not-allowed"
          : "border-slate-400 text-slate-400 hover:border-slate-500 hover:text-slate-500"
      )}
    >
      {icon}
    </button>
  );
};
