"use client"

import React, { useEffect, useState } from 'react'

import { LoadingContent } from '@/components/ui/LoadingSpinner'
import { useMessageChat } from '@/hooks/useChat'

import { SendChat } from './sendChat'
import { MessageList } from './MessageList'

export interface ChatInterfaceProps {
  conversationId: string
  promptId: string
  shopId: string
  userId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
}

export function ChatInterface({ conversationId: propConversationId, promptId, shopId, userId, onConversationUpdate }: ChatInterfaceProps) {
  // State for current conversation (can be updated via events)
  const [currentConversationId, setCurrentConversationId] = useState(propConversationId);

  // Use current conversation ID for messages
  const {
    messages,
    setMessages,
    isLoadingMessages,
    messagesEndRef,
    hasToken
  } = useMessageChat({ conversationId: currentConversationId })

  console.log('🖥️ [ChatInterface] Render - Messages count:', messages.length, 'Prop ID:', propConversationId, 'Current ID:', currentConversationId);

  // Listen for conversation changes from sidebar
  useEffect(() => {
    const handleConversationChange = (event: CustomEvent) => {
      const newConversationId = event.detail.conversationId;
      console.log('🔄 [ChatInterface] Received conversation change event:', newConversationId);
      console.log('🔄 [ChatInterface] Current conversation before change:', currentConversationId);

      if (newConversationId !== currentConversationId) {
        console.log('🔄 [ChatInterface] Updating conversation ID to:', newConversationId);
        setCurrentConversationId(newConversationId);
      } else {
        console.log('🔄 [ChatInterface] Same conversation ID, no change needed');
      }
    };

    window.addEventListener('conversationChange', handleConversationChange as EventListener);
    return () => window.removeEventListener('conversationChange', handleConversationChange as EventListener);
  }, [currentConversationId]);

  // Sync with prop changes (for initial load or direct URL access)
  useEffect(() => {
    console.log('🔄 [ChatInterface] Props changed - Prop ID:', propConversationId, 'Current ID:', currentConversationId);
    if (propConversationId !== currentConversationId) {
      console.log('🔄 [ChatInterface] Syncing with prop change:', propConversationId);
      setCurrentConversationId(propConversationId);
    }
  }, [propConversationId, currentConversationId]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      const pathParts = window.location.pathname.split('/');
      const urlConversationId = pathParts[pathParts.length - 1];

      if (urlConversationId && urlConversationId !== currentConversationId) {
        console.log('🔙 [ChatInterface] Browser navigation detected:', urlConversationId);
        setCurrentConversationId(urlConversationId);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [currentConversationId]);

  // Tự động scroll xuống cuối khi có tin nhắn mới
  React.useEffect(() => {
    if (messagesEndRef?.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Show loading state if no token
  if (!hasToken) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingContent text="Đang tải..." />
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen relative max-w-[1280px] mx-auto ">
      {/* Messages Area */}
      <MessageList
        messages={messages}
        isLoading={isLoadingMessages}
        messagesEndRef={messagesEndRef}
      />

      {/* SendChat Component - Fixed at bottom */}
      <SendChat
        conversationId={currentConversationId}
        promptId={promptId}
        onConversationUpdate={onConversationUpdate}
        messages={messages}
        setMessages={setMessages}
      />
    </div>
  )
}

export default ChatInterface;