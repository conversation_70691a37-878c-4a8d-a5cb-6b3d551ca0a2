"use client"

import { get<PERSON><PERSON><PERSON>, setCookie } from 'cookies-next/client';
import { useSession } from 'next-auth/react';
import { useEffect, useState, useCallback } from 'react';

import UserDropdown from '@/components/profile/UserDropdown';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { WebsiteBranding } from '@/components/ui/WebsiteBranding';
import { getAvatarFromProfile } from '@/lib/avatarUtils';
import { getUserProfile } from '@/services/userService';
import { useAuthStore } from '@/stores/authStore';

export default function Header() {
    const { data: session, status } = useSession();
    const [profile, setProfile] = useState<any>(null);

    const loadUserProfile = useCallback(async () => {
        // Store session info for later use
        let sessionInfo = null;
        if (session?.user) {
            sessionInfo = {
                name: session.user.name,
                email: session.user.email,
                avatar: session.user.image
            };
        }

        // Check for demo user
        const isDemoUser = sessionStorage.getItem('demo_logged_in') === 'true';
        if (isDemoUser) {
            const demoUserInfo = sessionStorage.getItem('demo_user_info');
            if (demoUserInfo) {
                try {
                    const userInfo = JSON.parse(demoUserInfo);
                    setProfile({
                        user: {
                            name: userInfo.name,
                            email: userInfo.email,
                            avatar: null
                        },
                        package: {
                            credit: 500,
                            credit_use: 0
                        }
                    });
                } catch (error) {
                    // Ignore demo user parsing errors
                }
            }
            return;
        }

        // Token-based authentication from auth store
        let token: string | null = null;
        let shop_id: string | null = null;
        let user_id: string | null = null;
        let prompt_id: string | null = null;
        let website_id: string | null = null;

        try {
            const authState = useAuthStore.getState();
            token = authState.getToken();
            shop_id = authState.shop_id;
            user_id = getCookie('fchatai_user_id') as string || null;
            prompt_id = authState.prompt_id;
        } catch (error) {
            token = getCookie('fchatai_token') as string || null;
            shop_id = getCookie('fchatai_shop_id') as string || null;
            user_id = getCookie('fchatai_user_id') as string || null;
        }

        // Lấy prompt_id và website_id từ global state
        try {
            const appState = require('@/stores/appStore');
            prompt_id = appState.useAppStore.getState().getPromptId();
            website_id = appState.useAppStore.getState().getWebsiteId();
            const domainData = appState.useAppStore.getState().domainConfig;
            console.log("website_id", website_id);
            console.log("domain data", domainData);
        } catch (error) {
            // fallback nếu không lấy được
        }

        if (!token) {
            if (sessionInfo) {
                setProfile({
                    user: sessionInfo,
                    package: {
                        credit: 1000,
                        credit_use: 0
                    }
                });
            }
            return;
        }

        // Extract shop_id from JWT token if not in auth store
        if (token && !shop_id) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                shop_id = payload.shop_id;
                user_id = payload.user_id || payload.sub;
                const userData: any = {};
                if (shop_id) userData.shop_id = shop_id;
                if (user_id) userData.user_id = user_id;
                if (Object.keys(userData).length > 0) {
                    useAuthStore.getState().setUserData(userData);
                }
            } catch (e) {
                // Ignore JWT decode errors
            }
        }

        try {
            const profileRes = await getUserProfile();
            if (profileRes && !profileRes.error && profileRes.data) {
                const mergedProfile = {
                    ...profileRes.data,
                    user: {
                        ...profileRes.data.user,
                        ...(sessionInfo && {
                            name: sessionInfo.name || profileRes.data.user?.name,
                            email: sessionInfo.email || profileRes.data.user?.email,
                            avatar: sessionInfo.avatar || profileRes.data.user?.avatar
                        })
                    }
                };
                setProfile(mergedProfile);
                // Lưu shop_id và user_id vào Zustand nếu chưa có
                if (!shop_id && mergedProfile.shop?._id) {
                    useAuthStore.getState().setUserData({ shop_id: mergedProfile.shop._id });
                }
                if (!user_id && mergedProfile.user?._id) {
                    useAuthStore.getState().setUserData({ user_id: mergedProfile.user._id });
                }
            } else {
                if (sessionInfo) {
                    setProfile({
                        user: sessionInfo,
                        package: {
                            credit: 1000,
                            credit_use: 0
                        }
                    });
                }
            }
        } catch (error) {
            if (sessionInfo) {
                setProfile({
                    user: sessionInfo,
                    package: {
                        credit: 1000,
                        credit_use: 0
                    }
                });
            }
        }
    }, [session]);

    // Watch for token changes via custom events (no more localStorage events)
    useEffect(() => {
        const handleTokenSet = () => {
            loadUserProfile();
        };

        window.addEventListener('tokenSet', handleTokenSet);

        return () => {
            window.removeEventListener('tokenSet', handleTokenSet);
        };
    }, []);

    // Immediate check when session changes
    useEffect(() => {
        if (status === 'loading') return; // Wait for session to load

        // Check immediately from auth store
        const token = useAuthStore.getState().getToken();
        if (token) {
            loadUserProfile();
            return;
        }

        // If no token but we have session, check once and then use a more reasonable interval
        if (session?.user) {
            // Check immediately first
            const currentToken = useAuthStore.getState().getToken();
            if (currentToken) {
                loadUserProfile();
                return;
            }

            // If no token found, set up a more reasonable polling interval
            let retryCount = 0;
            const maxRetries = 5; // 5 seconds total with 1000ms intervals

            const interval = setInterval(() => {
                retryCount++;
                const token = useAuthStore.getState().getToken();

                if (token) {
                    clearInterval(interval);
                    loadUserProfile();
                } else if (retryCount >= maxRetries) {
                    clearInterval(interval);
                    loadUserProfile(); // Load with session data only
                }
            }, 1000); // Check every 1 second instead of 50ms

            return () => clearInterval(interval);
        } else {
            // No session, load immediately (demo or direct access)
            loadUserProfile();
        }
    }, [session, status, loadUserProfile]);

    return (
        <header className="sticky top-0 z-20 w-full border-b border-gray-200 bg-white dark:border-slate-700 dark:bg-gray-900 flex items-center justify-between px-4 py-1.5">
            <div className="flex items-center gap-3">
                <SidebarTrigger className="-ml-1" />
                <WebsiteBranding
                    logoSize={46}
                    showTitle={true}
                    className="font-bold text-gray-800 sm:text-lg md:text-xl dark:text-slate-100"
                />
            </div>

            <div className="flex items-center justify-end gap-3 min-w-[200px]">
                <ThemeToggle />
                <UserDropdown user={(() => {
                    if (!profile) {
                        return {};
                    }

                    // Tạo avatar URL từ profile sử dụng utility function
                    const originalAvatar = (profile.user as any)?.avatar || (profile as any).avatar;
                    const avatarUrl = getAvatarFromProfile(profile, originalAvatar);

                    const userData = {
                        name: profile.user?.name || (profile as any).name,
                        email: profile.user?.email || (profile as any).email,
                        avatar: avatarUrl,
                        credit: profile.package?.credit || (profile as any).credit || 0,
                        credit_use: profile.package?.credit_use || (profile as any).credit_use || 0,
                    };

                    return userData;
                })()} />

            </div>
        </header>
    );
}
