"use client";
import { useState } from 'react';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { performCompleteLogout } from '@/lib/logoutUtils';
import { useToast } from '@/hooks/use-toast';

const colorClasses = [
    'bg-red-500',
    'bg-orange-500',
    'bg-amber-500',
    'bg-green-500',
    'bg-sky-500',
    'bg-blue-500',
    'bg-violet-500',
    'bg-pink-500',
];
function _emailToColorClass(email = '') {
    let hash = 0;
    for (let i = 0; i < email.length; i++) {
        hash = email.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colorClasses[Math.abs(hash) % colorClasses.length];
}

type UserDropdownProps = {
    user: {
        name?: string;
        email?: string;
        avatar?: string;
        credit?: number;
        credit_use?: number;
    };
};

export default function UserDropdown({ user }: UserDropdownProps) {
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const { toast } = useToast();

    const creditUsed = user.credit_use || 0;
    const creditTotal = user.credit || 0;
    const initial = user.name?.[0]?.toUpperCase() || user.email?.[0]?.toUpperCase() || 'U';

    const handleLogout = async () => {
        if (isLoggingOut) return; // Prevent double-click

        setIsLoggingOut(true);

        try {
            toast({
                title: "Đang đăng xuất...",
                description: "Vui lòng chờ trong giây lát",
            });

            await performCompleteLogout();
        } catch (error) {
            console.error('❌ Logout error:', error);
            toast({
                title: "Lỗi đăng xuất",
                description: "Có lỗi xảy ra khi đăng xuất. Vui lòng thử lại.",
                variant: "destructive",
            });
            setIsLoggingOut(false);
        }
    };
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                {user.avatar ? (
                    <img src={user.avatar} alt={user.name} className="ml-2 w-12 h-12 rounded-full object-cover" />
                ) : (
                    <button type="button" className={`ml-2 flex items-center justify-center w-12 h-12 rounded-full text-xl font-semibold text-white focus:outline-none bg-green-500`}>
                        {initial}
                    </button>
                )}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-72 p-0">
                <div className="p-4 flex flex-col items-center">
                    {user.avatar ? (
                        <img src={user.avatar} alt={user.name} className="w-12 h-12 rounded-full mb-2 object-cover" />
                    ) : (
                        <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl font-bold text-white mb-2 bg-green-500">{initial}</div>
                    )}
                    <div className="text-base font-semibold text-gray-900 dark:text-slate-100 truncate w-full text-center">{user.name}</div>
                    <div className="text-sm text-gray-500 dark:text-slate-400 truncate w-full text-center">{user.email}</div>
                    <div className="w-full mt-3">
                        <div className="flex justify-center text-sm text-gray-700 dark:text-slate-300 mb-2">
                            <span>Đã sử dụng: {creditUsed}/{creditTotal}</span>
                        </div>
                    </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className={`flex items-center gap-2 cursor-pointer ${isLoggingOut
                            ? 'text-gray-400 cursor-not-allowed'
                            : 'text-red-600 hover:text-red-700'
                        }`}
                >
                    {isLoggingOut ? (
                        <div className="mr-2 w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                    ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="mr-2">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a2 2 0 01-2 2H7a2 2 0 01-2-2V7a2 2 0 012-2h4a2 2 0 012 2v1" />
                        </svg>
                    )}
                    {isLoggingOut ? 'Đang đăng xuất...' : 'Đăng xuất'}
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
} 