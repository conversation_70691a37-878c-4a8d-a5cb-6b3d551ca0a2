import { useEffect, useState, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { useGlobalState } from '@/hooks/useGlobalState'
import { fetchPromptconversations as fetchPromptConversations, updateConversationName } from '@/services/userService'
import { deleteConversation } from '@/services/conversationService'
import { getPromptIdWithFallback } from '@/lib/apiUtils'
import { useToast } from '@/hooks/use-toast'

// Global flag to prevent multiple conversation loads
let isConversationsLoading = false
import type {
  ConversationItem,
  ConversationUpdateDetail,
  UseConversationProps
} from '@/types/conversation'

// Utility function to ensure unique conversations
const ensureUniqueConversations = (conversations: ConversationItem[]): ConversationItem[] => {
  const seen = new Set<string>()
  return conversations.filter(conversation => {
    if (seen.has(conversation.id)) {
      console.log('⚠️ Removing duplicate conversation with ID:', conversation.id)
      return false
    }
    seen.add(conversation.id)
    return true
  })
}



// Main conversation management hook
export const useConversation = (isAuthReady: boolean, props?: UseConversationProps) => {
  const router = useRouter()
  const { auth } = useGlobalState()
  const { toast } = useToast()

  // State management
  const [conversationsList, setConversationsList] = useState<ConversationItem[]>([])
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(props?.currentConversationId || null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastLoadTime, setLastLoadTime] = useState<number>(0)
  const [hasLoaded, setHasLoaded] = useState(false)
  const LOAD_CONVERSATIONS_COOLDOWN = 5000 // 5 seconds cooldown
  const [editingId, setEditingId] = useState<string | null>(null)
  const [editValue, setEditValue] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [isCreatingNew, setIsCreatingNew] = useState(false)

  // Use props if provided, otherwise use internal state
  const conversations = props?.conversations || conversationsList
  const setConversations = props?.setConversations || setConversationsList
  const token = props?.token || auth.token

  // Load conversations from API
  const loadConversations = useCallback(async () => {
    if (!isAuthReady || !auth.token || !auth.prompt_id) return

    // Skip if already loaded and has conversations data
    if (hasLoaded && conversations.length > 0) {
      console.log('✅ Conversations already loaded, skipping...')
      return
    }

    // Global flag to prevent concurrent loads
    if (isConversationsLoading) {
      console.log('🔄 Conversations already loading globally, skipping...')
      return
    }

    // Rate limiting to prevent excessive API calls
    const now = Date.now()
    if (now - lastLoadTime < LOAD_CONVERSATIONS_COOLDOWN) {
      console.log('⏳ Conversations load rate limited, skipping...')
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      setLastLoadTime(now)
      isConversationsLoading = true

      const conversationsRes = await fetchPromptConversations(auth.prompt_id, 1, 10, auth.token).catch((err: any) => {
        console.warn('Conversations load failed:', err)
        return { data: [] }
      })

      // Update conversations
      const safeConversations = Array.isArray(conversationsRes) ? conversationsRes :
        Array.isArray(conversationsRes?.data) ? conversationsRes.data : []

      setConversations(safeConversations)
      auth.setConversations(safeConversations) // Keep for backward compatibility
      setHasLoaded(true)

    } catch (error: any) {
      console.error('❌ Failed to load conversations:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
      isConversationsLoading = false
    }
  }, [isAuthReady, auth.token, auth.prompt_id, auth.setConversations, lastLoadTime, hasLoaded, conversations.length, LOAD_CONVERSATIONS_COOLDOWN])

  useEffect(() => {
    loadConversations()
  }, [loadConversations])

  // Navigation handlers
  const handleConversationChange = useCallback((id: string | null, shouldNavigate: boolean = false) => {
    setCurrentConversationId(id)

    // Only navigate if explicitly requested (for initial load or external navigation)
    if (shouldNavigate) {
      if (id && id !== '0') {
        router.push(`/chat/${id}`)
      } else {
        router.push('/chat/0')
      }
    }
  }, [router])

  // Conversation mutations
  const addConversation = useCallback((newConversation: ConversationItem) => {
    const updatedConversations = [newConversation, ...conversations]
    setConversations(updatedConversations)
    auth.setConversations(updatedConversations)
  }, [conversations, auth])

  const updateConversation = useCallback((conversationId: string, updates: Partial<ConversationItem>) => {
    const updatedConversations = conversations.map(conv =>
      conv.id === conversationId ? { ...conv, ...updates } : conv
    )
    setConversations(updatedConversations)
    auth.setConversations(updatedConversations)
  }, [conversations, auth])

  const removeConversation = useCallback((conversationId: string) => {
    const updatedConversations = conversations.filter(conv => conv.id !== conversationId)
    setConversations(updatedConversations)
    auth.setConversations(updatedConversations)
  }, [conversations, auth])

  const reorderConversations = useCallback((newOrder: ConversationItem[] | ((prev: ConversationItem[]) => ConversationItem[])) => {
    if (typeof newOrder === 'function') {
      const updatedConversations = newOrder(conversations)
      setConversations(updatedConversations)
      auth.setConversations(updatedConversations)
    } else {
      setConversations(newOrder)
      auth.setConversations(newOrder)
    }
  }, [conversations, auth])

  const clearConversations = useCallback(() => {
    setConversations([])
    auth.setConversations([])
  }, [auth])

  // Create new conversation
  const handleCreateNewConversation = useCallback(async () => {
    if (isCreatingNew) return

    setIsCreatingNew(true)
    try {
      const newConversation: ConversationItem = {
        id: '0',
        icon: '/favicon.ico',
        title: 'Cuộc trò chuyện mới',
        messages: [],
        conversation: {
          _id: '0',
          name: 'Cuộc trò chuyện mới',
          prompt_id: auth.prompt_id || getPromptIdWithFallback(),
        }
      }

      setConversations((prev: ConversationItem[]) => {
        const filteredConversations = Array.isArray(prev) ? prev.filter(conversation => conversation.id !== '0') : []
        const newConversations = [newConversation, ...filteredConversations]
        return ensureUniqueConversations(newConversations)
      })

      setCurrentConversationId('0')
      window.history.replaceState(null, '', '/chat/0')

      toast({
        title: "Thành công",
        description: "Đã bắt đầu cuộc trò chuyện mới",
      })
    } catch (error) {
      console.error('❌ Error creating new conversation:', error)
      toast({
        title: "Lỗi",
        description: 'Có lỗi xảy ra khi tạo cuộc trò chuyện mới',
        variant: "destructive",
      })
    } finally {
      setIsCreatingNew(false)
    }
  }, [isCreatingNew, auth.prompt_id, setConversations, toast])

  // Save conversation name
  const handleSaveConversationName = useCallback(async (conversationId: string, newName: string) => {
    const trimmedName = newName.trim()
    if (!trimmedName) {
      setEditingId(null)
      return
    }

    setIsSaving(true)
    try {
      const response = await updateConversationName(conversationId, trimmedName)

      if (!response.error) {
        setConversations((prev: ConversationItem[]) => Array.isArray(prev) ? prev.map(t =>
          t.id === conversationId ? { ...t, title: trimmedName } : t
        ) : [])
        toast({
          title: "Thành công",
          description: "Đã cập nhật tên cuộc trò chuyện",
        })
      } else {
        toast({
          title: "Lỗi",
          description: response.msg || 'Không thể cập nhật tên cuộc trò chuyện',
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Lỗi",
        description: 'Có lỗi xảy ra khi cập nhật tên cuộc trò chuyện',
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
      setEditingId(null)
    }
  }, [setConversations, toast])

  // Delete conversation
  const handleDeleteConversation = useCallback(async (conversationId: string) => {
    if (!token) {
      toast({
        title: "Lỗi",
        description: "Vui lòng đăng nhập để xóa cuộc trò chuyện",
        variant: "destructive",
      })
      return
    }

    const confirmed = window.confirm('Bạn có chắc chắn muốn xóa cuộc trò chuyện này không?')
    if (!confirmed) return

    try {
      const response = await deleteConversation(conversationId, token)

      if (!response.error) {
        setConversations((prev: ConversationItem[]) => Array.isArray(prev) ? prev.filter(t => t.id !== conversationId) : [])

        if (currentConversationId === conversationId) {
          setCurrentConversationId(null)
          window.history.replaceState(null, '', '/')
        }

        toast({
          title: "Thành công",
          description: "Đã xóa cuộc trò chuyện thành công",
        })
      } else {
        throw new Error(response.message || response.msg || 'Không thể xóa cuộc trò chuyện')
      }
    } catch (error: any) {
      console.error('❌ Error deleting conversation:', error)
      toast({
        title: "Lỗi",
        description: error.message || 'Có lỗi xảy ra khi xóa cuộc trò chuyện',
        variant: "destructive",
      })
    }
  }, [token, currentConversationId, setConversations, toast])

  // Conversation updates listener
  const useConversationUpdates = useCallback((props: { onNewConversation: (newId: string, newName?: string) => void }) => {
    useEffect(() => {
      const handleConversationUpdate = (event: CustomEvent<ConversationUpdateDetail>) => {
        const { oldId, newId, newName } = event.detail
        console.log('🔄 Conversation update received:', { oldId, newId, newName })

        if (oldId === '0' && newId !== '0') {
          props.onNewConversation(newId, newName)
        }
      }

      window.addEventListener('conversationUpdate', handleConversationUpdate as EventListener)
      return () => {
        window.removeEventListener('conversationUpdate', handleConversationUpdate as EventListener)
      }
    }, [props])
  }, [])

  // Memoized data
  const memoizedConversations = useMemo(() => conversations, [conversations])
  const conversationsCount = useMemo(() => conversations.length, [conversations])
  const recentConversations = useMemo(() => conversations.slice(0, 5), [conversations])
  const currentConversationInfo = useMemo(() => ({
    id: currentConversationId,
    isNewConversation: currentConversationId === '0' || currentConversationId === null,
    chatUrl: currentConversationId && currentConversationId !== '0' ? `/chat/${currentConversationId}` : '/chat/0'
  }), [currentConversationId])

  return {
    // Data
    conversations: memoizedConversations,
    conversationsCount,
    recentConversations,
    currentConversationId,
    currentConversationInfo,
    isLoading,
    error,
    editingId,
    editValue,
    isSaving,
    isCreatingNew,

    // Navigation
    setCurrentConversationId: handleConversationChange,
    handleConversationChange,

    // Mutations
    addConversation,
    updateConversation,
    removeConversation,
    setConversations: reorderConversations,
    clearConversations,

    // Actions
    loadConversations,
    handleCreateNewConversation,
    handleSaveConversationName,
    handleDeleteConversation,
    setEditingId,
    setEditValue,

    // Utils
    useConversationUpdates,
    reload: loadConversations
  }
}
