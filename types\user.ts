// User related types

/**
 * User information from authentication
 */
export interface UserInfo {
    name: string;
    email: string;
    image?: string;
    provider?: 'google' | 'email';
}

/**
 * Login credentials
 */
export interface LoginCredentials {
    email: string;
    password: string;
}

/**
 * Google profile information
 */
export interface GoogleProfile {
    email: string;
    name: string;
    avatar: string;
    website_id: string;
    ref: string;
    google_id: string;
}

/**
 * User entity from API
 */
export interface User {
    _id: string;
    name: string;
    email: string;
    username?: string;
    email_verify?: number;
    website_id?: string;
    ref?: string | null;
    status?: number;
    avatar?: string;
    credit?: number;
    credit_use?: number;
}

/**
 * Shop entity from API
 */
export interface Shop {
    _id: string;
    name: string;
    email: string;
    phone?: string;
    website_id?: string;
    username?: string;
    ref?: string | null;
    api_token?: string;
    status?: number;
    created_at?: string;
    updated_at?: string;
    credit?: number;
    credit_use?: number;
}

/**
 * Package entity from API
 */
export interface Package {
    _id: string;
    shop_id: string;
    package_id: number;
    month: number;
    agent: number;
    agent_used: number;
    capacity: number;
    credit: number;
    credit_use: number;
    credits_bonus: number;
    credits_bonus_used: number;
    package_system_id: number;
    expired_at?: string | null;
    created_at: string;
    updated_at: string;
}