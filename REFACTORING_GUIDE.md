# Hướng dẫn Cấu trúc Code Sau Tái cấu trúc

## Tổng quan

Dự án đã được tái cấu trúc theo các nguyên tắc clean code với mục tiêu:
- Tách riêng logic và UI
- Tạo components tái sử dụng
- Tổ chức types/interfaces theo domain
- Tối ưu hooks và state management
- Loại bỏ code trùng lặp

## Cấu trúc Thư mục

### `/types` - Type Definitions
```
types/
├── index.ts          # Export tập trung tất cả types
├── api.ts            # API response types, pagination
├── auth.ts           # Authentication & domain types
├── chat.ts           # Chat & message types
├── conversation.ts   # Conversation types
├── user.ts           # User, shop, package types
├── profile.ts        # Profile types
└── components.ts     # Component prop types
```

**Cải thiện:**
- Types được tổ chức theo domain
- Loại bỏ trùng lặp giữa các file
- Export tập trung qua index.ts
- JSDoc comments cho tất cả interfaces

### `/hooks` - Custom Hooks
```
hooks/
├── useAuthData.ts     # Hook tái sử dụng cho auth data
├── useAuthStore.ts    # Optimized auth store hooks
├── useDomainAuth.ts   # Domain-aware authentication
├── useMessages.ts     # Message management
├── useChat.ts         # Simplified chat hook
└── useSendChatForm.ts # Chat form logic
```

**Cải thiện:**
- Tách logic khỏi UI components
- Hooks tái sử dụng với dependencies tối ưu
- Separation of concerns rõ ràng
- Performance optimization với useMemo/useCallback

### `/services` - Business Logic
```
services/
├── baseService.ts        # Base class cho tất cả services
├── chatService.ts        # Chat API & streaming logic
├── conversationService.ts # Conversation CRUD operations
├── authService.ts        # Authentication services
└── userService.ts        # User management
```

**Cải thiện:**
- Business logic tách khỏi components
- Base service class để tái sử dụng
- Consistent error handling
- API call patterns chuẩn hóa

### `/components` - UI Components
```
components/
├── ui/
│   ├── LoadingSpinner.tsx  # Reusable loading states
│   ├── EmptyState.tsx      # Empty state components
│   └── ChatInput.tsx       # Reusable chat input
├── chat/
│   ├── ChatInterface.tsx   # Main chat interface
│   ├── MessageList.tsx     # Message display component
│   └── sendChat.tsx        # Chat input wrapper
└── auth/
    └── DomainGuard.tsx
```

**Cải thiện:**
- UI components tách khỏi business logic
- Reusable components với props interface rõ ràng
- Composition pattern thay vì inheritance
- Consistent styling và behavior

### `/stores` - State Management
```
stores/
├── authStore.v2.ts    # Optimized auth store
├── authStore.ts       # Legacy store (backward compatibility)
└── appStore.ts        # App-wide state
```

**Cải thiện:**
- Zustand store với selectors tối ưu
- Subscription optimization với useShallow
- Computed values và memoization
- Clear separation of state và actions

## Patterns Được Áp dụng

### 1. Separation of Concerns
- **UI Components**: Chỉ handle rendering và user interaction
- **Hooks**: Business logic và state management
- **Services**: API calls và data processing
- **Types**: Type definitions tập trung

### 2. Composition over Inheritance
```tsx
// Trước: Monolithic component
<ChatInterface {...allProps} />

// Sau: Composed components
<ChatInterface>
  <MessageList messages={messages} />
  <ChatInput onSubmit={handleSubmit} />
</ChatInterface>
```

### 3. Custom Hooks Pattern
```tsx
// Tách logic thành custom hooks
const useMessages = (conversationId) => {
  // Message loading, updating logic
  return { messages, isLoading, addMessage, updateMessage }
}

// Component chỉ handle UI
const MessageList = ({ conversationId }) => {
  const { messages, isLoading } = useMessages(conversationId)
  return <div>{/* UI only */}</div>
}
```

### 4. Service Layer Pattern
```tsx
// Base service với common functionality
class BaseService {
  protected static async get<T>() { /* common logic */ }
  protected static async post<T>() { /* common logic */ }
}

// Specific services extend base
class ChatService extends BaseService {
  static async sendMessage() { /* specific logic */ }
}
```

## Migration Guide

### Updating Imports
```tsx
// Cũ
import { Message, ChatRequest } from '@/types/index'

// Mới - specific imports
import { Message, ChatRequest } from '@/types/chat'
import { UserProfile } from '@/types/profile'
```

### Using New Hooks
```tsx
// Cũ - direct store access
const { auth } = useGlobalState()

// Mới - optimized hooks
const { token, isAuthenticated } = useAuthData()
const { messages, isLoading } = useMessages(conversationId)
```

### Service Usage
```tsx
// Cũ - direct API calls
const response = await apiCall('GET', [...])

// Mới - service methods
const response = await ConversationService.getMessages(conversationId)
```

## Performance Optimizations

1. **Selective Subscriptions**: Hooks chỉ subscribe vào state cần thiết
2. **Memoization**: useMemo/useCallback cho expensive operations
3. **Component Splitting**: Tách components nhỏ để giảm re-renders
4. **Service Caching**: Cache API responses khi phù hợp

## Best Practices

1. **Type Safety**: Tất cả functions/components có type definitions
2. **Error Handling**: Consistent error handling patterns
3. **Documentation**: JSDoc comments cho public APIs
4. **Testing**: Components và hooks dễ test hơn
5. **Maintainability**: Code dễ đọc và maintain

## Backward Compatibility

- Legacy hooks và services vẫn hoạt động
- Gradual migration strategy
- Export compatibility layers khi cần

## Next Steps

1. Migrate existing components sang pattern mới
2. Add unit tests cho hooks và services
3. Performance monitoring và optimization
4. Documentation updates
