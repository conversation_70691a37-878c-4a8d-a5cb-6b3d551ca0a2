import React from 'react'
import ReactMarkdown from 'react-markdown'

interface MarkdownRendererProps {
  content: string | null | undefined
  className?: string
}

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = ''
}) => {
  // Handle null/undefined content
  if (!content) {
    return <div className={`markdown-content ${className}`}></div>
  }

  // Pre-process content to handle line breaks
  const processedContent = content
    .replace(/\n\n/g, '\n\n') // Keep double line breaks
    .replace(/\n(?!\n)/g, '  \n') // Convert single line breaks to markdown line breaks

  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown>
        {processedContent}
      </ReactMarkdown>
    </div>
  )
}

export default MarkdownRenderer
