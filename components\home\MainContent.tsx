"use client";


import { useEffect, useMemo } from 'react';

import { getPromptIdWithFallback, getStoredShopId } from '@/lib/apiUtils';
import type { MainContentProps } from '@/types/components';
import { ensureUniqueConversations, createNewConversation } from '@/utils/conversation';
import './main-ai-bg.css';
import { ChatInterface } from '@components/chat/ChatInterface';



export default function MainContent({ conversations, setConversations, currentConversationId, setCurrentConversationId }: MainContentProps) {
    // Memoize conversation existence check for better performance
    const hasNewConversation = useMemo(() =>
        conversations.some(conversation => conversation.id === '0'),
        [conversations]
    );

    // Auto-create new conversation when no current conversation is selected (run only once)
    useEffect(() => {
        if (!currentConversationId && !hasNewConversation) {
            const newConversation = createNewConversation('0', '<PERSON><PERSON><PERSON><PERSON> trò chuyện mới');

            // Ensure no duplicates when adding new conversation
            setConversations(prev => {
                const filteredConversations = prev.filter(conversation => conversation.id !== '0');
                const newConversations = [newConversation, ...filteredConversations];
                return ensureUniqueConversations(newConversations);
            });
            setCurrentConversationId('0');
        } else if (!currentConversationId && hasNewConversation) {
            // If new conversation exists but not selected, select it
            setCurrentConversationId('0');
        }
    }, [currentConversationId, hasNewConversation, setConversations, setCurrentConversationId]);

    // Handle conversation update from ChatInterface
    const handleConversationUpdate = (oldId: string, newId: string, newName?: string) => {

        // Update conversations list
        setConversations(prev => prev.map(conversation =>
            conversation.id === oldId
                ? {
                    ...conversation,
                    id: newId,
                    title: newName || conversation.title,
                    conversation: conversation.conversation ? {
                        ...conversation.conversation,
                        _id: newId,
                        name: newName || conversation.conversation.name
                    } : undefined
                }
                : conversation
        ));

        // Update current conversation ID and URL without full page reload
        setCurrentConversationId(newId);

        // Update URL without full page reload
        window.history.replaceState(null, '', `/chat/${newId}`);

        // Trigger sidebar update for new conversations
        setTimeout(() => {
            window.dispatchEvent(new CustomEvent('conversationUpdated', {
                detail: { oldId, newId, newName }
            }));
        }, 500);
    };

    // Get values from storage using apiUtils
    const promptId = getPromptIdWithFallback();
    const shopId = getStoredShopId() || '';
    const userId = ''; // Will be extracted from token or session

    return (
        <main className="flex-1 flex flex-col items-center justify-center relative bg-white dark:bg-gray-900 overflow-hidden" role="main">
            {useMemo(() => (
                <div className="z-10 w-full h-full">
                    <ChatInterface
                        conversationId={currentConversationId || '0'}
                        promptId={promptId}
                        shopId={shopId}
                        userId={userId}
                        onConversationUpdate={handleConversationUpdate}
                    />
                </div>
            ), [currentConversationId, promptId, shopId, userId, handleConversationUpdate])}

        </main>
    );
} 