import NextAuth from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import { loginWithGoogleProfile } from '@/services/authService'

const handler = NextAuth({
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile"
        }
      }
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      // Chỉ xử lý đăng nhập Google
      if (account?.provider === 'google' && user.email) {
        try {
          console.log('🔐 Processing Google sign-in for:', user.email)

          // Chuẩn bị dữ liệu profile để gửi đến backend
          const googleProfile = {
            email: user.email,
            name: user.name || '',
            avatar: user.image || '',
            website_id: process.env.NEXT_PUBLIC_WEBSITE_ID || '',
            ref: process.env.NEXT_PUBLIC_REF || '',
            google_id: user.id || '',
          }

          console.log('📤 Sending Google profile to backend:', googleProfile)

          // Gọi API backend để xác thực và lấy token
          const response = await loginWithGoogleProfile(googleProfile)

          if (response?.data) {
            console.log('✅ Backend authentication successful')
            // Lưu token vào user object để sử dụng trong JWT callback
            user.backendToken = response.data
            return true
          } else {
            console.error('❌ Backend authentication failed:', response)
            return false
          }
        } catch (error) {
          console.error('❌ Error during Google sign-in:', error)
          return false
        }
      }

      return true
    },
    async jwt({ token, account, user }) {
      // Persist the OAuth access_token and backend token
      if (account) {
        console.log('🔍 OAuth account info:', {
          provider: account.provider,
          type: account.type,
          access_token: account.access_token ? 'present' : 'missing'
        })
        token.accessToken = account.access_token
        token.provider = account.provider
      }

      // Lưu backend token từ signIn callback
      if (user?.backendToken) {
        token.backendToken = user.backendToken
        console.log('💾 Backend token saved to JWT')
      }

      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      session.accessToken = token.accessToken as string
      session.provider = token.provider as string
      session.backendToken = token.backendToken as string
      return session
    },
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET,
})

export { handler as GET, handler as POST }
