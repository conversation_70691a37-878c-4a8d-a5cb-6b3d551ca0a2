import React from 'react';
import MarkdownRenderer from '@/components/ui/MarkdownRenderer';
import { LoadingContent } from '@/components/ui/LoadingSpinner';
import { EmptyMessages } from '@/components/ui/EmptyState';
import type { Message } from '@/types';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  className?: string;
}

/**
 * Message list component for displaying chat messages
 * Separated from ChatInterface for better reusability
 */
export const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  messagesEndRef,
  className = ""
}) => {
  // Loading state
  if (isLoading) {
    return (
      <div className={`flex-1 overflow-y-auto ${className}`}>
        <LoadingContent text="Đang tải tin nhắn..." />
      </div>
    );
  }

  // Empty state
  if (messages.length === 0) {
    return (
      <div className={`flex-1 overflow-y-auto ${className}`}>
        <EmptyMessages />
      </div>
    );
  }

  // Messages list
  return (
    <div className={`flex-1 overflow-y-auto p-4 space-y-4 pb-32 ${className}`}>
      {/* Sort messages by created_at to ensure chronological order */}
      {[...messages]
        .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        .map((message) => (
          <MessageItem key={message._id} message={message} />
        ))}
      <div ref={messagesEndRef} />
    </div>
  );
};

/**
 * Individual message item component
 */
interface MessageItemProps {
  message: Message;
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isUser = message.type === 1;
  const isAI = message.type === 0;

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div
        className={`max-w-[80%] p-3 rounded-lg ${isUser
          ? 'bg-blue-500 text-white'
          : 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100'
          }`}
      >
        {isUser ? (
          <UserMessage content={message.content} />
        ) : (
          <AIMessage content={message.content} />
        )}
      </div>
    </div>
  );
};

/**
 * User message component
 */
const UserMessage: React.FC<{ content: string }> = ({ content }) => {
  return (
    <p className="whitespace-pre-wrap break-words">
      {content}
    </p>
  );
};

/**
 * AI message component with markdown support
 */
const AIMessage: React.FC<{ content: string }> = ({ content }) => {
  return (
    <div className="prose prose-sm max-w-none dark:prose-invert">
      <MarkdownRenderer content={content} />
    </div>
  );
};
