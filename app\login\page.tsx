"use client"

import { getC<PERSON><PERSON>, setCookie } from 'cookies-next/client';
import { ArrowLeft } from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { signIn, useSession } from 'next-auth/react'
import React, { useState, useEffect } from 'react'
import { toast } from 'sonner'

import { saveTokenToCookie } from '@/lib/apiUtils';
import { isGoogleLoginEnabled as checkGoogleLoginEnabled } from '@/lib/googleAuthConfig';
import { loginWithEmailPassword, loginWithGoogleProfile } from '@/services/authService';
import { getUserProfile } from '@/services/userService';
import { useAuthStore } from '@/stores/authStore';


export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isGoogleLoginEnabled, setIsGoogleLoginEnabled] = useState(true)
  const [isProcessingLogin, setIsProcessingLogin] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const { data: session, status } = useSession()
  const router = useRouter()

  // Check domain config for Google login availability
  useEffect(() => {
    const enabled = checkGoogleLoginEnabled();
    setIsGoogleLoginEnabled(enabled);
  }, []);

  // Test login API function
  const testLoginAPI = async () => {
    console.log('🧪 Testing login API...');
    try {
      const testProfile = {
        email: '<EMAIL>',
        name: 'Test User',
        avatar: '',
        website_id: process.env.NEXT_PUBLIC_WEBSITE_ID || '',
        ref: process.env.NEXT_PUBLIC_REF || '',
        google_id: 'test123',
      };

      console.log('🚀 Calling loginWithGoogleProfile with test data...');
      const response = await loginWithGoogleProfile(testProfile);
      console.log('📊 Test API Response:', response);

      if (response) {
        console.log('✅ API is working! Token received:', response.data);
      } else {
        console.log('❌ API failed:', response);
      }
    } catch (error) {
      console.error('❌ API test error:', error);
    }
  };

  // Make test function available globally
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).testLoginAPI = testLoginAPI;
    }
  }, []);

  React.useEffect(() => {
    const handleGoogleSession = async () => {
      if (status === 'authenticated' && session) {
        console.log('📊 Full session object:', session);

        // Kiểm tra xem có backend token không (đã được xử lý trong next-auth callback)
        if ((session as any).backendToken) {
          console.log('🎉 Backend token found in session:', (session as any).backendToken);

          // Lưu token vào cookie
          saveTokenToCookie((session as any).backendToken);

          // Lấy profile để có user ID và gán image
          try {
            const profileRes = await getUserProfile((session as any).backendToken);
            if (profileRes && profileRes.data && session.user?.email) {
              const profileData = profileRes.data as any;
              const userId = profileData.user?._id || profileData._id || profileData.user?.id || profileData.id;

              // Lưu thông tin user vào auth store với image = userid.png
              useAuthStore.getState().setUserInfo({
                name: session.user.name || '',
                email: session.user.email,
                image: userId ? `https://fchat.ai/avatar/${userId}.png` : (session.user.image || ''),
                provider: 'google',
              });
              console.log('✅ Updated user info with profile image from Google session:', userId ? `https://fchat.ai/avatar/${userId}.png` : 'fallback');
            } else {
              // Fallback nếu không lấy được profile
              useAuthStore.getState().setUserInfo({
                name: session.user.name || '',
                email: session.user.email || '',
                image: session.user.image || '',
                provider: 'google',
              });
            }
          } catch (error) {
            console.warn('Failed to get profile or store user info:', error);
            // Fallback nếu có lỗi
            if (session.user?.email) {
              try {
                useAuthStore.getState().setUserInfo({
                  name: session.user.name || '',
                  email: session.user.email,
                  image: session.user.image || '',
                  provider: 'google',
                });
              } catch (fallbackError) {
                console.warn('Failed to store user info in fallback:', fallbackError);
              }
            }
          }

          // Chuyển hướng về trang chủ
          toast.success('Đăng nhập Google thành công!');
          router.push('/');
          return;
        }

        // Fallback: nếu không có backend token, thử xử lý theo cách cũ
        const sessionId = session.user?.email + '_' + (session as any).accessToken?.substring(0, 10);
        const lastProcessedSession = sessionStorage.getItem('lastProcessedSession');

        if (lastProcessedSession === sessionId || isProcessingLogin) {
          console.log('🔄 Session already processed or currently processing, skipping...');
          return;
        }

        setIsProcessingLogin(true);

        // Check if this is a different user than stored in auth store
        try {
          const authState = useAuthStore.getState();
          const storedUserEmail = authState.userInfo?.email;
          const currentUserEmail = session.user?.email;

          if (storedUserEmail && storedUserEmail !== currentUserEmail) {
            console.log('🔄 Different user detected, clearing old tokens...');
            console.log('  - Stored email:', storedUserEmail);
            console.log('  - Current email:', currentUserEmail);

            // Clear auth store
            authState.clearAll();
          }
        } catch (error) {
          console.warn('Failed to check user change:', error);
        }

        // Store current user info in auth store (no more localStorage)
        if (session.user?.email) {
          try {
            useAuthStore.getState().setUserInfo({
              name: session.user.name || '',
              email: session.user.email,
              image: session.user.image || '',
              provider: 'google',
            });
          } catch (error) {
            console.warn('Failed to store user info in auth store:', error);
          }
        }

        // Session authenticated, will get user token from API
        const profile = {
          email: session.user?.email || '',
          name: session.user?.name || '',
          avatar: session.user?.image || '',
          website_id: process.env.NEXT_PUBLIC_WEBSITE_ID || '',
          ref: process.env.NEXT_PUBLIC_REF || '',
          google_id: session.user?.id || '',
        };
        console.log('Google profile:', profile);
        if (profile.email && profile.name) {
          console.log('🚀 Calling loginWithGoogleProfile API...');
          loginWithGoogleProfile(profile)
            .then(async data => {
              console.log('📊 LoginWithGoogleProfile API response:', data);
              console.log('🎉 GOOGLE LOGIN SUCCESS - Full Response:', JSON.stringify(data, null, 2));

              if (data && data.data) {
                // Mark this session as processed
                sessionStorage.setItem('lastProcessedSession', sessionId);

                console.log('🔑 TOKEN RECEIVED:', data.data);
                console.log('🔑 TOKEN TYPE:', typeof data.data);
                console.log('🔑 TOKEN LENGTH:', data.data?.length);

                // Validate token is string before saving
                if (typeof data.data === 'string') {
                  console.log('💾 SAVING TOKEN TO COOKIE...');
                  saveTokenToCookie(data.data);
                  console.log('✅ TOKEN SAVED SUCCESSFULLY');
                } else {
                  console.error('❌ Invalid token format from Google login:', typeof data.data);
                  toast.error('Lỗi: Token không hợp lệ từ Google');
                  return;
                }
                // Get shop_id from auth store or fetch from profile
                let shop_id: string | null = null;
                try {
                  shop_id = useAuthStore.getState().shop_id;
                } catch (error) {
                  shop_id = getCookie('fchatai_shop_id') as string || null;
                }

                if (!shop_id) {
                  try {
                    const profileRes = await getUserProfile(data.data);
                    if (profileRes && profileRes.data) {
                      shop_id = (profileRes.data as any).shop?.shop_id || (profileRes.data as any).shop?._id || (profileRes.data as any).package?.shop_id || (profileRes.data as any).user?.shop_id || null;
                      if (shop_id) {
                        // Save to auth store
                        try {
                          useAuthStore.getState().setUserData({ shop_id });
                        } catch (error) {
                          setCookie('fchatai_shop_id', shop_id, {
                            maxAge: 7 * 24 * 60 * 60,
                            path: '/',
                            sameSite: 'lax',
                          });
                        }
                      }

                      // Cập nhật user info với image từ profile response
                      const profileData = profileRes.data as any;
                      const userId = profileData.user?._id || profileData._id || profileData.user?.id || profileData.id;

                      if (userId && session.user?.email) {
                        try {
                          useAuthStore.getState().setUserInfo({
                            name: session.user.name || '',
                            email: session.user.email,
                            image: `https://fchat.ai/avatar/${userId}.png`, // Gán image = userid.png
                            provider: 'google',
                          });
                        } catch (error) {
                          console.warn('Failed to update user info with profile image:', error);
                        }
                      }
                    }
                  } catch { }
                }

                toast.success('Đăng nhập thành công!');

                // Add delay before redirect to ensure all state is saved
                setTimeout(() => {
                  router.push('/');
                }, 500);
              } else {
                toast.error(data?.msg || 'Đăng nhập Google thất bại!');
                setIsProcessingLogin(false);
              }
            })
            .catch((err) => {
              console.error('❌ LOGIN API ERROR:', err);
              console.error('🔍 Error details:', {
                message: err.message,
                response: err.response?.data,
                status: err.response?.status
              });
              toast.error('Lỗi kết nối tới server!');
              console.error('loginWithGoogleProfile error:', err);
              setIsProcessingLogin(false);
            });
        } else {
          console.warn('Google profile missing email or name:', profile);
        }
      }
    };

    handleGoogleSession();
  }, [status, session, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      console.log('🚀 Calling loginWithEmailPassword API...');
      const data = await loginWithEmailPassword(formData.email, formData.password);

      if (data && data.error === false && data.data) {
        // Extract token from response data
        let token: string | null = null;

        if (typeof data.data === 'string') {
          token = data.data;
        } else if (typeof data.data === 'object' && data.data !== null) {
          const dataObj = data.data as any;
          if (dataObj.token) {
            token = dataObj.token;
          } else if (dataObj.access_token) {
            token = dataObj.access_token;
          } else if (dataObj.jwt) {
            token = dataObj.jwt;
          } else {
            toast.error('Lỗi: Không thể lấy token từ server');
            return;
          }
        } else {
          toast.error('Lỗi: Dữ liệu trả về không hợp lệ');
          return;
        }

        if (!token || typeof token !== 'string') {
          toast.error('Lỗi: Token không hợp lệ');
          return;
        }

        // Validate JWT format
        const tokenParts = token.split('.');
        if (tokenParts.length !== 3) {
          toast.error('Lỗi: Token không đúng định dạng JWT');
          return;
        }

        saveTokenToCookie(token);

        // Get shop_id from auth store or fetch from profile
        let shop_id: string | null = null;
        try {
          shop_id = useAuthStore.getState().shop_id;
        } catch (error) {
          // Fallback to cookie
          shop_id = getCookie('fchatai_shop_id') as string || null;
        }

        if (!shop_id) {
          try {
            const profileRes = await getUserProfile();
            if (profileRes && profileRes.data) {
              shop_id = (profileRes.data as any).shop?.shop_id || (profileRes.data as any).shop?._id || (profileRes.data as any).package?.shop_id || (profileRes.data as any).user?.shop_id || null;
              if (shop_id) {
                // Save to auth store
                try {
                  useAuthStore.getState().setUserData({ shop_id });
                } catch (error) {
                  // Fallback to cookie
                  setCookie('fchatai_shop_id', shop_id, {
                    maxAge: 7 * 24 * 60 * 60,
                    path: '/',
                    sameSite: 'lax',
                  });
                }
              }

              // Cập nhật user info với image từ profile response (cho email login)
              const profileData = profileRes.data as any;
              const userId = profileData.user?._id || profileData._id || profileData.user?.id || profileData.id;
              const userEmail = profileData.user?.email || profileData.email || formData.email;
              const userName = profileData.user?.name || profileData.name || formData.email.split('@')[0];

              if (userId && userEmail) {
                try {
                  useAuthStore.getState().setUserInfo({
                    name: userName,
                    email: userEmail,
                    image: `https://fchat.ai/avatar/${userId}.png`, // Gán image = userid.png
                    provider: 'email',
                  });
                  console.log('✅ Updated user info with profile image for email login:', `https://fchat.ai/avatar/${userId}.png`);
                } catch (error) {
                  console.warn('Failed to update user info with profile image for email login:', error);
                }
              }
            }
          } catch { }
        }
        toast.success('Đăng nhập thành công!');
        router.push('/');
      } else {
        throw new Error(data?.msg || data?.message || 'Đăng nhập thất bại!');
      }
    } catch (error: any) {
      toast.error(error.message || 'Đăng nhập thất bại. Vui lòng thử lại.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true)
    try {
      toast.info('Đang chuyển hướng đến Google...')

      // Sử dụng next-auth để đăng nhập Google
      // Việc xử lý backend sẽ được thực hiện trong signIn callback của next-auth
      const result = await signIn('google', {
        callbackUrl: '/',
        redirect: false
      })

      if (result?.error) {
        toast.error('Đăng nhập Google thất bại: ' + result.error)
      } else if (result?.ok) {
        // Đăng nhập thành công, session sẽ được cập nhật và useEffect sẽ xử lý
        toast.success('Đăng nhập Google thành công!')
      }
    } catch (error) {
      console.error('Google login error:', error)
      toast.error('Đăng nhập Google thất bại. Vui lòng thử lại.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen w-full bg-white dark:bg-gray-900">
      <a
        className="absolute top-6 left-6 z-10 hidden cursor-pointer lg:block"
        onClick={e => {
          e.preventDefault();
          if (window.history.length > 1) {
            router.back();
          } else {
            router.push('/');
          }
        }}
        href="/"
      >
        <button
          type="button"
          aria-label="Quay lại"
          title="Quay lại"
          className="inline-flex items-center justify-center h-10 w-10 rounded-full text-sm font-medium transition hover:bg-accent hover:text-accent-foreground dark:hover:bg-gray-900 focus-visible:ring-2 focus-visible:ring-ring/50"
        >
          <ArrowLeft className="h-5 w-5 dark:text-slate-200" />
        </button>
      </a>
      {/* Sidebar image (left) */}
      <div className="hidden md:block md:w-1/2 relative overflow-hidden">
        <Image
          src="/left-image.png"
          alt="FChat.ai - Trợ lý AI thông minh"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/10 to-transparent"></div>
      </div>
      {/* Login form (right) */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-6 min-h-screen">
        <div className="w-full max-w-md mx-auto p-8 flex flex-col items-center space-y-6">
          <div className="flex flex-col items-center">
            <Image
              src="/favicon.ico"
              alt="FChat.ai"
              width={64}
              height={64}
              className="rounded-2xl object-contain mb-2"
              priority
            />
            <h1 className="text-3xl font-bold text-center">FChat.ai</h1>
            <p className="text-sm text-gray-500 text-center mb-2">Fchat.ai - Trợ lý AI thông minh hỗ trợ viết nội dung, hình ảnh và giọng nói</p>
          </div>
          <h2 className="text-2xl font-semibold text-center mb-2">Đăng nhập</h2>

          {/* Google Login - Conditionally rendered based on domain config */}
          {isGoogleLoginEnabled ? (
            <>
              <button
                type="button"
                onClick={handleGoogleLogin}
                disabled={isLoading}
                className="w-full h-12 bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 rounded-full shadow-lg hover:shadow-xl flex items-center justify-center gap-3 font-medium"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="w-5 h-5"><g><path fill="#4285F4" d="M24 9.5c3.54 0 6.7 1.22 9.19 3.23l6.85-6.85C35.64 2.36 30.23 0 24 0 14.82 0 6.71 5.82 2.69 14.09l7.98 6.19C12.13 13.19 17.57 9.5 24 9.5z" /><path fill="#34A853" d="M46.1 24.55c0-1.64-.15-3.22-.43-4.74H24v9.01h12.42c-.54 2.9-2.18 5.36-4.64 7.04l7.19 5.6C43.93 37.13 46.1 31.36 46.1 24.55z" /><path fill="#FBBC05" d="M10.67 28.28a14.5 14.5 0 0 1 0-8.56l-7.98-6.19A23.94 23.94 0 0 0 0 24c0 3.82.92 7.43 2.69 10.65l7.98-6.19z" /><path fill="#EA4335" d="M24 48c6.23 0 11.44-2.06 15.25-5.6l-7.19-5.6c-2.01 1.35-4.6 2.15-8.06 2.15-6.43 0-11.87-3.69-13.33-8.65l-7.98 6.19C6.71 42.18 14.82 48 24 48z" /><path fill="none" d="M0 0h48v48H0z" /></g></svg>
                Đăng nhập với tài khoản Google
              </button>
              <div className="flex items-center gap-2 w-full">
                <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700" />
                <span className="text-xs text-gray-400">Hoặc</span>
                <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700" />
              </div>
            </>
          ) : (
            <div className="w-full text-center">
              <p className="text-sm text-gray-500 mb-4">
                Đăng nhập bằng email và mật khẩu
              </p>
            </div>
          )}
          <form onSubmit={handleEmailLogin} className="space-y-4 w-full">
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">Email</label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={handleInputChange}
                className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2  focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                placeholder="Nhập email"
                disabled={isLoading}
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-1">Mật khẩu</label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2  focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm pr-10"
                  placeholder="Nhập mật khẩu"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute inset-y-0 right-2 flex items-center text-gray-400 hover:text-gray-600 text-sm"
                  onClick={() => setShowPassword(v => !v)}
                  aria-label={showPassword ? 'Ẩn mật khẩu' : 'Hiện mật khẩu'}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-5.523 0-10-4.477-10-10 0-1.657.403-3.22 1.125-4.575m1.875-2.425A9.956 9.956 0 0112 3c5.523 0 10 4.477 10 10 0 1.657-.403-3.22-1.125-4.575m-1.875 2.425A9.956 9.956 0 0112 21c-2.21 0-4.267-.72-5.925-1.95M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0zm7.5 0a9.956 9.956 0 01-1.125 4.575m-1.875 2.425A9.956 9.956 0 0112 21c-2.21 0-4.267-.72-5.925-1.95m-1.875-2.425A9.956 9.956 0 012.5 12c0-1.657.403-3.22 1.125-4.575m1.875-2.425A9.956 9.956 0 0112 3c2.21 0 4.267.72 5.925 1.95" /></svg>
                  )}
                </button>
              </div>
            </div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full h-10 rounded-3xl bg-green-500 hover:bg-green-600 text-white font-semibold transition-colors duration-200 flex items-center justify-center gap-2"
            >
              {isLoading && (
                <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin inline-block mr-2"></span>
              )}
              Đăng nhập
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
