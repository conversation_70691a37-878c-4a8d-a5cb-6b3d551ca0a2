import { useState } from 'react'
import { useMessages } from '@/hooks/useMessages'
import { useAuthData, useChatPayload } from '@/hooks/useAuthData'
import { ChatService, chatUtils } from '@/services/chatService'

import type { Message } from '@/types'

/**
 * Hook for handling chat form submission
 * Separated business logic from UI components
 */
interface UseSendChatFormProps {
  conversationId: string
  promptId: string
  onConversationUpdate?: (oldId: string, newId: string, newName?: string) => void
  messages?: Message[]
  setMessages?: React.Dispatch<React.SetStateAction<Message[]>>
}

export const useSendChatForm = ({ conversationId, promptId, onConversationUpdate, messages: externalMessages, setMessages: externalSetMessages }: UseSendChatFormProps) => {
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Get auth data and chat utilities
  const { hasToken } = useAuthData()
  const { createPayload } = useChatPayload()

  // Use external messages state if provided, otherwise use internal
  const internalMessagesHook = useMessages(externalMessages ? '' : conversationId) // Only use internal if no external
  const messages = externalMessages || internalMessagesHook.messages
  const setMessages = externalSetMessages || internalMessagesHook.setMessages

  console.log('🔧 [useSendChatForm] Using messages from:', externalMessages ? 'EXTERNAL' : 'INTERNAL', 'for conversation:', conversationId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading || !hasToken) return

    console.log('🚀 Starting chat submission:', {
      conversationId,
      query: inputValue,
      promptId,
      hasToken
    });

    // Create user message
    const userMessage = ChatService.createTempUserMessage(inputValue, conversationId)
    console.log('👤 Created user message:', userMessage);

    setMessages((prev: Message[]) => {
      const newMessages = [...prev, userMessage];
      return newMessages;
    })
    setIsLoading(true)

    try {
      // Create chat payload
      const payload = createPayload(conversationId, inputValue, promptId)

      // Send message via chat service
      const stream = await ChatService.sendMessage(payload)

      if (stream) {
        const reader = stream.getReader()
        let aiContent = ''

        // Create AI message placeholder
        const aiMessage = ChatService.createTempAIMessage(conversationId)

        setMessages((prev: Message[]) => {
          const newMessages = [...prev, aiMessage];
          return newMessages;
        })

        // Process streaming response
        await chatUtils.processStreamingResponse(
          reader,
          (content) => {
            aiContent += content
            setMessages((prev: Message[]) => {
              const updatedMessages = prev.map((msg: Message) =>
                msg._id === aiMessage._id ? { ...msg, content: aiContent } : msg
              );
              return updatedMessages;
            })
          },
          () => { }
        )
      } else {
        console.log('❌ No stream received from API');
      }
    } catch (error) {
      console.error('Error sending message:', error)
      // Remove user message on error
      setMessages((prev: Message[]) => prev.filter((msg: Message) => msg._id !== userMessage._id))
    } finally {
      setIsLoading(false)
      setInputValue('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  return {
    inputValue,
    setInputValue,
    isLoading,
    handleSubmit,
    handleKeyPress
  }
}